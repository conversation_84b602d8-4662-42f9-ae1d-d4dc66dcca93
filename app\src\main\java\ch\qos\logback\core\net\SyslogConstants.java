package ch.qos.logback.core.net;

public class SyslogConstants {
    public static final int ALERT_SEVERITY = 1;
    public static final int CRITICAL_SEVERITY = 2;
    public static final int DEBUG_SEVERITY = 7;
    public static final int EMERGENCY_SEVERITY = 0;
    public static final int ERROR_SEVERITY = 3;
    public static final int INFO_SEVERITY = 6;
    public static final int LOG_ALERT = 112;
    public static final int LOG_AUDIT = 104;
    public static final int LOG_AUTH = 32;
    public static final int LOG_AUTHPRIV = 80;
    public static final int LOG_CLOCK = 120;
    public static final int LOG_CRON = 72;
    public static final int LOG_DAEMON = 24;
    public static final int LOG_FTP = 88;
    public static final int LOG_KERN = 0;
    public static final int LOG_LOCAL0 = 128;
    public static final int LOG_LOCAL1 = 136;
    public static final int LOG_LOCAL2 = 144;
    public static final int LOG_LOCAL3 = 152;
    public static final int LOG_LOCAL4 = 160;
    public static final int LOG_LOCAL5 = 168;
    public static final int LOG_LOCAL6 = 176;
    public static final int LOG_LOCAL7 = 184;
    public static final int LOG_LPR = 48;
    public static final int LOG_MAIL = 16;
    public static final int LOG_NEWS = 56;
    public static final int LOG_NTP = 96;
    public static final int LOG_SYSLOG = 40;
    public static final int LOG_USER = 8;
    public static final int LOG_UUCP = 64;
    public static final int NOTICE_SEVERITY = 5;
    public static final int SYSLOG_PORT = 514;
    public static final int WARNING_SEVERITY = 4;
}
