package ch.qos.logback.core.spi;

import ch.qos.logback.core.filter.Filter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public final class FilterAttachableImpl<E> implements FilterAttachable<E> {
    CopyOnWriteArrayList<Filter<E>> filterList = new CopyOnWriteArrayList<>();

    @Override
    public void addFilter(Filter<E> filter) {
        this.filterList.add(filter);
    }

    @Override
    public void clearAllFilters() {
        this.filterList.clear();
    }

    @Override
    public List<Filter<E>> getCopyOfAttachedFiltersList() {
        return new ArrayList(this.filterList);
    }

    @Override
    public FilterReply getFilterChainDecision(E e) {
        Iterator<Filter<E>> it = this.filterList.iterator();
        while (it.hasNext()) {
            FilterReply decide = it.next().decide(e);
            if (decide == FilterReply.DENY || decide == FilterReply.ACCEPT) {
                return decide;
            }
        }
        return FilterReply.NEUTRAL;
    }
}
