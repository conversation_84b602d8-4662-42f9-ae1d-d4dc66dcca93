package ch.qos.logback.core.net.ssl;

import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;

public class KeyStoreFactoryBean {
    private String location;
    private String password;
    private String provider;
    private String type;

    private KeyStore newKeyStore() throws NoSuchAlgorithmException, NoSuchProviderException, KeyStoreException {
        return getProvider() != null ? KeyStore.getInstance(getType(), getProvider()) : KeyStore.getInstance(getType());
    }

    public java.security.KeyStore createKeyStore() throws java.security.NoSuchProviderException, java.security.NoSuchAlgorithmException, java.security.KeyStoreException {
        
//本方法所在的代码反编译失败，请在反编译界面按照提示打开jeb编译器，找到当前对应的类的相应方法，替换到这里，然后进行适当的代码修复工作


eturn null;//这行代码是为了保证方法体完整性额外添加的，请按照上面的方法补充完善代码

//throw new UnsupportedOperationException(
Method not decompiled: ch.qos.logback.core.net.ssl.KeyStoreFactoryBean.createKeyStore():java.security.KeyStore");
    }

    public String getLocation() {
        return this.location;
    }

    public String getPassword() {
        return this.password == null ? SSL.DEFAULT_KEYSTORE_PASSWORD : this.password;
    }

    public String getProvider() {
        return this.provider;
    }

    public String getType() {
        return this.type == null ? SSL.DEFAULT_KEYSTORE_TYPE : this.type;
    }

    public void setLocation(String str) {
        this.location = str;
    }

    public void setPassword(String str) {
        this.password = str;
    }

    public void setProvider(String str) {
        this.provider = str;
    }

    public void setType(String str) {
        this.type = str;
    }
}
