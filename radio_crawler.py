#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考拉FM电台遍历脚本
根据分析的API协议遍历所有电台的URL和名称
"""

import requests
import json
import time
import urllib.parse
from typing import List, Dict, Set
import hashlib
import random

class KaolaFMCrawler:
    def __init__(self):
        self.base_url = "http://api.kaolafm.com/api/v3.1/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'KaolaFM/3.1.0 (Android; API 23)',
            'Accept': 'application/json',
            'Content-Type': 'application/json; charset=utf-8'
        })
        
        # 模拟设备参数
        self.device_params = {
            'installid': self._generate_install_id(),
            'uid': '',
            'udid': self._generate_udid(),
            'sessionid': self._generate_session_id(),
            'imsi': '',
            'operator': '1',
            'lon': '116.397128',
            'lat': '39.916527',
            'network': '1',
            'timestamp': str(int(time.time())),
            'playid': self._generate_play_id(),
            'sign': ''
        }
        
        self.radio_list = []
        self.processed_ids = set()
    
    def _generate_install_id(self) -> str:
        """生成安装ID"""
        return str(random.randint(100000000, 999999999))
    
    def _generate_udid(self) -> str:
        """生成设备唯一标识"""
        return hashlib.md5(str(random.random()).encode()).hexdigest()
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return hashlib.md5(str(time.time()).encode()).hexdigest()
    
    def _generate_play_id(self) -> str:
        """生成播放ID"""
        return str(random.randint(1000000, 9999999))
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整的API URL"""
        if '?' in endpoint:
            url = endpoint + '&'
        else:
            url = endpoint + '?'
        
        # 添加通用参数
        params = []
        for key, value in self.device_params.items():
            if value:
                params.append(f"{key}={urllib.parse.quote(str(value))}")
        
        return url + '&'.join(params)
    
    def _make_request(self, url: str) -> Dict:
        """发起HTTP请求"""
        try:
            full_url = self._build_url(url)
            print(f"请求URL: {full_url}")
            
            response = self.session.get(full_url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('status') == 200:
                return data.get('data', {})
            else:
                print(f"API返回错误: {data}")
                return {}
                
        except Exception as e:
            print(f"请求失败: {e}")
            return {}
    
    def get_categories(self, fid: str = "0") -> List[Dict]:
        """获取分类列表"""
        url = f"{self.base_url}category/list?fid={fid}"
        data = self._make_request(url)
        
        categories = []
        if 'dataList' in data:
            categories = data['dataList']
            print(f"获取到 {len(categories)} 个分类")
        
        return categories
    
    def get_public_radio_list(self, category_ids: str) -> List[Dict]:
        """获取公共电台列表"""
        url = f"{self.base_url}typeradio/list?ids={category_ids}"
        data = self._make_request(url)
        
        radios = []
        if 'dataList' in data:
            for item in data['dataList']:
                if 'dataList' in item:
                    radios.extend(item['dataList'])
        
        return radios
    
    def get_recommend_radios(self) -> List[Dict]:
        """获取推荐电台"""
        url = f"{self.base_url}play/recommendradio"
        data = self._make_request(url)
        
        radios = []
        if 'dataList' in data:
            radios = data['dataList']
            print(f"获取到 {len(radios)} 个推荐电台")
        
        return radios
    
    def search_radios(self, words: str = "", cid: int = 0, page_size: int = 50, page_num: int = 1) -> List[Dict]:
        """搜索电台"""
        url = f"{self.base_url}resource/search?words={urllib.parse.quote(words)}&cid={cid}&sorttype=0&pagesize={page_size}&pagenum={page_num}"
        data = self._make_request(url)
        
        radios = []
        if 'dataList' in data and 'dataList' in data['dataList']:
            radios = data['dataList']['dataList']
            print(f"搜索到 {len(radios)} 个电台")
        
        return radios
    
    def extract_radio_info(self, radio_data: Dict) -> Dict:
        """提取电台信息"""
        radio_info = {}
        
        # 电台名称
        if 'rname' in radio_data:
            radio_info['name'] = radio_data['rname']
        elif 'title' in radio_data:
            radio_info['name'] = radio_data['title']
        elif 'name' in radio_data:
            radio_info['name'] = radio_data['name']
        else:
            radio_info['name'] = '未知电台'
        
        # 电台ID和URL
        if 'rid' in radio_data:
            radio_id = radio_data['rid']
            radio_type = radio_data.get('rtype', '0')
            radio_info['url'] = f"http://api.kaolafm.com/api/v3.1/resource/radioinfo?rtype={radio_type}&rid={radio_id}"
        elif 'oid' in radio_data:
            radio_id = radio_data['oid']
            radio_type = radio_data.get('type', '0')
            radio_info['url'] = f"http://api.kaolafm.com/api/v3.1/resource/radioinfo?rtype={radio_type}&rid={radio_id}"
        else:
            radio_info['url'] = '未知URL'
        
        # 其他信息
        radio_info['description'] = radio_data.get('radioDesc', radio_data.get('des', ''))
        radio_info['pic'] = radio_data.get('pic', radio_data.get('img', ''))
        radio_info['web_url'] = radio_data.get('webUrl', '')
        
        return radio_info
    
    def crawl_all_radios(self):
        """遍历所有电台"""
        print("开始遍历考拉FM电台...")
        
        # 1. 获取推荐电台
        print("\n=== 获取推荐电台 ===")
        recommend_radios = self.get_recommend_radios()
        for radio in recommend_radios:
            info = self.extract_radio_info(radio)
            radio_id = radio.get('rid', radio.get('oid', ''))
            if radio_id and radio_id not in self.processed_ids:
                self.radio_list.append(info)
                self.processed_ids.add(radio_id)
        
        time.sleep(1)  # 避免请求过快
        
        # 2. 获取分类和分类下的电台
        print("\n=== 获取分类电台 ===")
        categories = self.get_categories()
        
        for category in categories:
            category_id = category.get('id', category.get('categoryId', ''))
            category_name = category.get('name', category.get('categoryName', ''))
            
            if category_id:
                print(f"处理分类: {category_name} (ID: {category_id})")
                
                # 获取该分类下的电台
                radios = self.get_public_radio_list(str(category_id))
                for radio in radios:
                    info = self.extract_radio_info(radio)
                    radio_id = radio.get('rid', radio.get('oid', ''))
                    if radio_id and radio_id not in self.processed_ids:
                        self.radio_list.append(info)
                        self.processed_ids.add(radio_id)
                
                time.sleep(0.5)  # 避免请求过快
        
        # 3. 通过搜索获取更多电台
        print("\n=== 通过搜索获取电台 ===")
        search_keywords = ['音乐', '新闻', '娱乐', '体育', '财经', '科技', '情感', '小说', '相声', '脱口秀']
        
        for keyword in search_keywords:
            print(f"搜索关键词: {keyword}")
            radios = self.search_radios(keyword, page_size=50)
            for radio in radios:
                info = self.extract_radio_info(radio)
                radio_id = radio.get('rid', radio.get('oid', ''))
                if radio_id and radio_id not in self.processed_ids:
                    self.radio_list.append(info)
                    self.processed_ids.add(radio_id)
            
            time.sleep(1)  # 避免请求过快
        
        print(f"\n总共获取到 {len(self.radio_list)} 个电台")
    
    def save_radio_list(self, filename: str = "radio_list.txt"):
        """保存电台列表到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for radio in self.radio_list:
                f.write(f"{radio['name']},{radio['url']}\n")
        
        print(f"电台列表已保存到 {filename}")
    
    def save_detailed_info(self, filename: str = "radio_detailed.json"):
        """保存详细信息到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.radio_list, f, ensure_ascii=False, indent=2)
        
        print(f"详细信息已保存到 {filename}")

def main():
    crawler = KaolaFMCrawler()
    
    try:
        crawler.crawl_all_radios()
        crawler.save_radio_list()
        crawler.save_detailed_info()
        
        print("\n电台遍历完成！")
        print(f"生成的文件:")
        print("- radio_list.txt: 电台名称和URL列表")
        print("- radio_detailed.json: 详细电台信息")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n发生错误: {e}")

if __name__ == "__main__":
    main()
