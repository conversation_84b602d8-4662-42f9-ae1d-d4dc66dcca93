#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试考拉FM API连接
"""

import urllib.request
import urllib.parse
import json
import time

def test_api():
    """测试API连接"""
    print("测试考拉FM API连接...")
    
    # 基础URL
    base_url = "http://api.kaolafm.com/api/v3.1/"
    
    # 模拟设备参数
    params = {
        'installid': '123456789',
        'uid': '',
        'udid': 'test_udid_123',
        'sessionid': 'test_session_123',
        'imsi': '',
        'operator': '1',
        'lon': '116.397128',
        'lat': '39.916527',
        'network': '1',
        'timestamp': str(int(time.time())),
        'playid': '1234567',
        'sign': ''
    }
    
    # 构建参数字符串
    param_str = '&'.join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items() if v])
    
    # 测试推荐电台API
    test_url = f"{base_url}play/recommendradio?{param_str}"
    
    print(f"测试URL: {test_url}")
    
    try:
        # 设置请求头
        req = urllib.request.Request(test_url)
        req.add_header('User-Agent', 'KaolaFM/3.1.0 (Android; API 23)')
        req.add_header('Accept', 'application/json')
        
        # 发起请求
        with urllib.request.urlopen(req, timeout=10) as response:
            data = response.read().decode('utf-8')
            print(f"响应状态码: {response.status}")
            print(f"响应内容: {data[:500]}...")  # 只显示前500个字符
            
            # 解析JSON
            try:
                json_data = json.loads(data)
                print(f"JSON解析成功，状态: {json_data.get('status')}")
                if 'data' in json_data and 'dataList' in json_data['data']:
                    radios = json_data['data']['dataList']
                    print(f"获取到 {len(radios)} 个推荐电台")
                    
                    # 显示前几个电台信息
                    for i, radio in enumerate(radios[:3]):
                        name = radio.get('rname', radio.get('title', '未知'))
                        rid = radio.get('rid', '未知')
                        print(f"电台 {i+1}: {name} (ID: {rid})")
                        
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                
    except Exception as e:
        print(f"请求失败: {e}")

def test_category_api():
    """测试分类API"""
    print("\n测试分类API...")
    
    base_url = "http://api.kaolafm.com/api/v3.1/"
    
    # 模拟设备参数
    params = {
        'installid': '123456789',
        'timestamp': str(int(time.time())),
        'network': '1'
    }
    
    param_str = '&'.join([f"{k}={v}" for k, v in params.items()])
    test_url = f"{base_url}category/list?fid=0&{param_str}"
    
    print(f"分类API URL: {test_url}")
    
    try:
        req = urllib.request.Request(test_url)
        req.add_header('User-Agent', 'KaolaFM/3.1.0 (Android; API 23)')
        
        with urllib.request.urlopen(req, timeout=10) as response:
            data = response.read().decode('utf-8')
            print(f"分类API响应: {data[:300]}...")
            
            json_data = json.loads(data)
            if 'data' in json_data and 'dataList' in json_data['data']:
                categories = json_data['data']['dataList']
                print(f"获取到 {len(categories)} 个分类")
                
                for i, cat in enumerate(categories[:5]):
                    name = cat.get('name', cat.get('categoryName', '未知'))
                    cat_id = cat.get('id', cat.get('categoryId', '未知'))
                    print(f"分类 {i+1}: {name} (ID: {cat_id})")
                    
    except Exception as e:
        print(f"分类API请求失败: {e}")

def simple_crawl():
    """简单的电台爬取"""
    print("\n开始简单电台爬取...")
    
    radio_list = []
    
    # 测试几个固定的API
    apis = [
        "http://api.kaolafm.com/api/v3.1/play/recommendradio",
        "http://api.kaolafm.com/api/v3.1/category/list?fid=0"
    ]
    
    for api_url in apis:
        try:
            # 添加基本参数
            if '?' in api_url:
                full_url = f"{api_url}&timestamp={int(time.time())}&network=1"
            else:
                full_url = f"{api_url}?timestamp={int(time.time())}&network=1"
            
            print(f"请求: {full_url}")
            
            req = urllib.request.Request(full_url)
            req.add_header('User-Agent', 'KaolaFM/3.1.0 (Android; API 23)')
            
            with urllib.request.urlopen(req, timeout=10) as response:
                data = json.loads(response.read().decode('utf-8'))
                
                if data.get('status') == 200 and 'data' in data:
                    if 'dataList' in data['data']:
                        items = data['data']['dataList']
                        for item in items:
                            name = item.get('rname', item.get('name', item.get('title', '未知电台')))
                            rid = item.get('rid', item.get('id', item.get('oid', '')))
                            rtype = item.get('rtype', item.get('type', '0'))
                            
                            if rid:
                                url = f"http://api.kaolafm.com/api/v3.1/resource/radioinfo?rtype={rtype}&rid={rid}"
                                radio_list.append((name, url))
                                print(f"添加电台: {name}")
                
        except Exception as e:
            print(f"API请求失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    # 保存结果
    if radio_list:
        with open('simple_radio_list.txt', 'w', encoding='utf-8') as f:
            for name, url in radio_list:
                f.write(f"{name},{url}\n")
        
        print(f"\n成功获取 {len(radio_list)} 个电台，已保存到 simple_radio_list.txt")
    else:
        print("\n未获取到任何电台数据")

if __name__ == "__main__":
    test_api()
    test_category_api()
    simple_crawl()
